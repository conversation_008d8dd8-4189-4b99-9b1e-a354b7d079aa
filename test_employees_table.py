#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار جدول العمال المطور
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from database import init_db, get_session
from ui.employees import EmployeesWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار جدول العمال المطور")
        self.setGeometry(100, 100, 1400, 900)
        
        # إنشاء قاعدة البيانات والجلسة
        init_db()
        session = get_session()
        
        # إنشاء واجهة العمال
        employees_widget = EmployeesWidget(session)
        self.setCentralWidget(employees_widget)

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(2)  # RTL
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
