@echo off
chcp 65001 >nul
title برنامج المحاسبة الإداري
echo.
echo ========================================
echo    برنامج المحاسبة الإداري
echo ========================================
echo.
echo اختر الإصدار:
echo [1] الإصدار العادي (main.py)
echo [2] الإصدار الصامت (main_silent.py) - موصى به
echo [3] خروج
echo.
set /p choice="اختر رقم الإصدار (1، 2، أو 3): "

if "%choice%"=="1" (
    echo.
    echo تشغيل الإصدار العادي...
    echo معلومات الدخول: admin / admin
    echo.
    venv\Scripts\python.exe main.py
) else if "%choice%"=="2" (
    echo.
    echo تشغيل الإصدار الصامت...
    echo معلومات الدخول: admin / admin
    echo.
    venv\Scripts\python.exe main_silent.py
) else if "%choice%"=="3" (
    echo وداعاً!
    exit /b 0
) else (
    echo.
    echo اختيار غير صحيح، سيتم تشغيل الإصدار الصامت...
    echo معلومات الدخول: admin / admin
    echo.
    venv\Scripts\python.exe main_silent.py
)

echo.
echo تم إغلاق البرنامج.
pause
