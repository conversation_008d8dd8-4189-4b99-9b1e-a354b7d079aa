#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار جدول الموردين المطور
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from database import init_db, get_session
from ui.suppliers import SuppliersWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار جدول الموردين المطور")
        self.setGeometry(100, 100, 1200, 800)
        
        # إنشاء قاعدة البيانات والجلسة
        init_db()
        session = get_session()
        
        # إنشاء واجهة الموردين
        suppliers_widget = SuppliersWidget(session)
        self.setCentralWidget(suppliers_widget)

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(2)  # RTL
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
