from PyQt5.QtWidgets import (<PERSON>W<PERSON>t, QVBox<PERSON><PERSON>out, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter
import re

from database import Supplier
from utils import format_currency

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.current_filter_value = None
        self.init_ui()

    def init_ui(self):
        """إنشاء واجهة الموردين مطابقة للفواتير"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # العنوان الرئيسي
        title_label = QLabel("🚛 إدارة الموردين")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 8px;
                margin: 2px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث والتصفية
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 8px;
                max-height: 60px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # عنصر البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 14px;
                font-weight: bold;
                padding: 4px;
            }
        """)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في الموردين...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border: 2px solid #2563eb;
                background: #f8fafc;
            }
        """)

        # قائمة التصفية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 14px;
                font-weight: bold;
                padding: 4px;
            }
        """)

        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات",
            "نشط (رصيد موجب)",
            "عادي (رصيد صفر)",
            "مدين (رصيد سالب)"
        ])
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                min-width: 200px;
            }
            QComboBox:hover {
                border: 2px solid #2563eb;
                background: #f8fafc;
            }
        """)

        # إضافة العناصر للتخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(filter_label)
        search_layout.addWidget(self.status_filter)
        search_frame.setLayout(search_layout)

        # إنشاء الجدول
        self.create_suppliers_table()

        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addWidget(search_frame)
        main_layout.addWidget(self.suppliers_table, 1)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.status_filter.currentTextChanged.connect(self.on_filter_changed)

        # تحميل البيانات
        self.create_sample_data_if_empty()
        self.refresh_data()

    def create_suppliers_table(self):
        """إنشاء جدول الموردين مطابق تماماً للفواتير"""
        # إنشاء الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(7)

        # عناوين الأعمدة مع الأيقونات مطابقة تماماً للفواتير
        headers = [
            "🆔 الرقم التسلسلي",
            "🚛 اسم المورد",
            "📍 العنوان",
            "📧 البريد الإلكتروني",
            "📞 رقم الهاتف",
            "💰 الرصيد",
            "🎯 حالة المورد"
        ]
        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مطابقة تماماً للفواتير
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setAlternatingRowColors(False)
        self.suppliers_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة تماماً للفواتير
        self.suppliers_table.verticalHeader().setDefaultSectionSize(50)
        self.suppliers_table.verticalHeader().setVisible(False)

        header = self.suppliers_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق التصميم والتفاعل مطابق للفواتير
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def on_filter_changed(self, text):
        """معالج تغيير التصفية"""
        try:
            if text == "نشط (رصيد موجب)":
                self.current_filter_value = "active"
            elif text == "عادي (رصيد صفر)":
                self.current_filter_value = "normal"
            elif text == "مدين (رصيد سالب)":
                self.current_filter_value = "debtor"
            else:
                self.current_filter_value = None

            # تطبيق التصفية
            self.filter_suppliers()
        except Exception as e:
            pass

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = self.current_filter_value

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية البيانات: {str(e)}")

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول مطابقة للفواتير"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.suppliers_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.suppliers_table.viewport())
            paint_watermark(painter, self.suppliers_table.viewport().rect())
            painter.end()

        self.suppliers_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول مطابق للفواتير"""
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.cellDoubleClicked.connect(self.edit_supplier)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.suppliers_table.itemAt(event.pos())
            if item is None:
                self.suppliers_table.clearSelection()
            QTableWidget.mousePressEvent(self.suppliers_table, event)

        self.suppliers_table.mousePressEvent = mousePressEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول مطابق للفواتير"""
        selected_items = self.suppliers_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد (إذا كانت موجودة)
        if hasattr(self, 'edit_button'):
            self.edit_button.setEnabled(has_selection)
        if hasattr(self, 'delete_button'):
            self.delete_button.setEnabled(has_selection)

    def edit_supplier(self):
        """تعديل بيانات مورد - دالة مؤقتة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"تعديل المورد رقم: {supplier_id}")
            # TODO: إضافة نافذة تعديل المورد
        except Exception as e:
            print(f"خطأ في تعديل المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """استخراج معرف المورد المحدد من الجدول مطابق للفواتير"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار مورد من القائمة"

            if not self.suppliers_table.item(selected_row, 0):
                return None, "الرجاء اختيار مورد صالح من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.suppliers_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم المورد"

            supplier_id = int(numbers[0])
            return supplier_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف المورد: {str(e)}"

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
            self.update_summary(suppliers)
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def update_summary(self, suppliers):
        """تحديث ملخص الموردين مطابق للفواتير"""
        try:
            # حساب الإحصائيات
            total_suppliers = len(suppliers)
            active_suppliers = len([s for s in suppliers if (s.balance or 0) > 0])
            debtor_suppliers = len([s for s in suppliers if (s.balance or 0) < 0])
            normal_suppliers = len([s for s in suppliers if (s.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(s.balance for s in suppliers if (s.balance or 0) > 0)
            total_negative = sum(abs(s.balance) for s in suppliers if (s.balance or 0) < 0)
            net_balance = sum(s.balance or 0 for s in suppliers)

            # تحديث النصوص (إذا كان هناك عنصر ملخص)
            if hasattr(self, 'summary_label'):
                summary_text = (f"إجمالي الموردين: {total_suppliers} | "
                              f"نشط: {active_suppliers} | "
                              f"مدين: {debtor_suppliers} | "
                              f"عادي: {normal_suppliers}")
                self.summary_label.setText(summary_text)

            if hasattr(self, 'balance_label'):
                balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                              f"الأرصدة السالبة: {format_currency(total_negative)} | "
                              f"صافي الرصيد: {format_currency(net_balance)}")
                self.balance_label.setText(balance_text)

        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {str(e)}")

    def populate_table(self, suppliers):
        """ملء الجدول بالبيانات مطابق للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.suppliers_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.suppliers_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للفواتير
            for row, supplier in enumerate(suppliers):
                try:
                    self.suppliers_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة مطابق للفواتير
                    id_item = QTableWidgetItem(f"🆔 #{str(supplier.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🆔 الرقم التسلسلي: {supplier.id}")
                    self.suppliers_table.setItem(row, 0, id_item)

                    # 2. اسم المورد مع تنسيق احترافي
                    name_text = supplier.name if supplier.name and supplier.name.strip() else "No data"
                    name_item = QTableWidgetItem(f"🚛 {name_text}")
                    name_item.setTextAlignment(Qt.AlignCenter)
                    name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if name_text == "No data":
                        name_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        name_item.setForeground(QColor("#1e40af"))
                    name_item.setToolTip(f"🚛 اسم المورد: {name_text}\n💡 انقر نقرتين للتعديل")
                    self.suppliers_table.setItem(row, 1, name_item)

                    # 3. العنوان مع تنسيق جذاب
                    address_text = supplier.address if supplier.address and supplier.address.strip() else "No data"
                    address_item = QTableWidgetItem(f"📍 {address_text}")
                    address_item.setTextAlignment(Qt.AlignCenter)
                    address_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if address_text == "No data":
                        address_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        address_item.setForeground(QColor("#065f46"))
                    address_item.setToolTip(f"📍 العنوان: {address_text}")
                    self.suppliers_table.setItem(row, 2, address_item)

                    # 4. البريد الإلكتروني مع تنسيق أنيق
                    email_text = supplier.email if supplier.email and supplier.email.strip() else "No data"
                    email_item = QTableWidgetItem(f"📧 {email_text}")
                    email_item.setTextAlignment(Qt.AlignCenter)
                    email_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if email_text == "No data":
                        email_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        email_item.setForeground(QColor("#374151"))
                    email_item.setToolTip(f"📧 البريد الإلكتروني: {email_text}")
                    self.suppliers_table.setItem(row, 3, email_item)

                    # 5. رقم الهاتف مع تنسيق متطور
                    phone_text = supplier.phone if supplier.phone and supplier.phone.strip() else "No data"
                    phone_item = QTableWidgetItem(f"📞 {phone_text}")
                    phone_item.setTextAlignment(Qt.AlignCenter)
                    phone_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if phone_text == "No data":
                        phone_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        phone_item.setForeground(QColor("#374151"))
                    phone_item.setToolTip(f"📞 رقم الهاتف: {phone_text}")
                    self.suppliers_table.setItem(row, 4, phone_item)

                    # 6. الرصيد مع تنسيق مالي متطور مطابق للفواتير
                    try:
                        balance = supplier.balance or 0
                        if balance and balance != 0:
                            balance_formatted = f"{int(abs(balance)):,}".replace(',', '٬')
                            if balance > 0:
                                balance_display = f"💎 {balance_formatted} جنيه"
                                balance_color = QColor("#059669")  # أخضر للموجب
                            else:
                                balance_display = f"💸 {balance_formatted} جنيه"
                                balance_color = QColor("#dc2626")  # أحمر للسالب
                        else:
                            balance_display = "💰 0 جنيه"
                            balance_color = QColor("#6b7280")  # رمادي للصفر
                    except Exception:
                        balance_display = "💰 0 جنيه"
                        balance_color = QColor("#6b7280")

                    balance_item = QTableWidgetItem(balance_display)
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    balance_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    balance_item.setForeground(balance_color)
                    balance_item.setToolTip(f"💰 رصيد المورد: {balance_display}")
                    self.suppliers_table.setItem(row, 5, balance_item)

                    # 7. حالة المورد مع تصميم متطور ومؤشرات بصرية مطابق للفواتير
                    status_map = {
                        'active': {
                            'text': '🎉 نشط',
                            'color': QColor("#059669"),
                            'tooltip': '🎉 المورد نشط ولديه رصيد موجب\n✅ يمكن التعامل معه'
                        },
                        'normal': {
                            'text': '🎯 عادي',
                            'color': QColor("#d97706"),
                            'tooltip': '🎯 المورد برصيد صفر\n💡 حالة عادية'
                        },
                        'debtor': {
                            'text': '⚠️ مدين',
                            'color': QColor("#dc2626"),
                            'tooltip': '⚠️ المورد مدين برصيد سالب\n🔄 يتطلب متابعة'
                        }
                    }

                    # تحديد حالة المورد
                    if balance > 0:
                        status_key = 'active'
                    elif balance == 0:
                        status_key = 'normal'
                    else:
                        status_key = 'debtor'

                    status_info = status_map.get(status_key, {
                        'text': f"❓ غير محدد",
                        'color': QColor("#6b7280"),
                        'tooltip': f"❓ حالة غير معروفة"
                    })

                    status_item = QTableWidgetItem(status_info['text'])
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    status_item.setForeground(status_info['color'])
                    status_item.setToolTip(status_info['tooltip'])
                    self.suppliers_table.setItem(row, 6, status_item)

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.suppliers_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.suppliers_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول الموردين: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود موردين
            supplier_count = self.session.query(Supplier).count()

            if supplier_count == 0:
                # إنشاء موردين تجريبيين
                sample_suppliers = [
                    Supplier(
                        name="شركة الأهرام للتوريدات",
                        address="القاهرة، مصر الجديدة",
                        phone="01234567890",
                        email="<EMAIL>",
                        balance=15000.0
                    ),
                    Supplier(
                        name="مؤسسة النيل التجارية",
                        address="الجيزة، المهندسين",
                        phone="01098765432",
                        email="<EMAIL>",
                        balance=-5000.0
                    ),
                    Supplier(
                        name="شركة الدلتا للمواد",
                        address="الإسكندرية، سموحة",
                        phone="01555123456",
                        email="<EMAIL>",
                        balance=0.0
                    ),
                    Supplier(
                        name="مجموعة الصعيد للتجارة",
                        address="أسوان، وسط البلد",
                        phone="01777888999",
                        email="<EMAIL>",
                        balance=25000.0
                    ),
                    Supplier(
                        name="شركة البحر الأحمر",
                        address="الغردقة، السقالة",
                        phone="01666555444",
                        email="<EMAIL>",
                        balance=-2500.0
                    )
                ]

                # إضافة الموردين لقاعدة البيانات
                for supplier in sample_suppliers:
                    self.session.add(supplier)

                # حفظ التغييرات
                self.session.commit()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()