from PyQt5.QtWidgets import (<PERSON>W<PERSON>t, QVBox<PERSON><PERSON>out, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter
import re

from database import Supplier
from utils import format_currency

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.current_filter_value = None
        self.init_ui()

    def init_ui(self):
        """إنشاء واجهة الموردين مطابقة للفواتير"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # العنوان الرئيسي
        title_label = QLabel("🚛 إدارة الموردين")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 8px;
                margin: 2px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث والتصفية
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 8px;
                max-height: 60px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # عنصر البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 14px;
                font-weight: bold;
                padding: 4px;
            }
        """)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في الموردين...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border: 2px solid #2563eb;
                background: #f8fafc;
            }
        """)

        # قائمة التصفية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 14px;
                font-weight: bold;
                padding: 4px;
            }
        """)

        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات",
            "نشط (رصيد موجب)",
            "عادي (رصيد صفر)",
            "مدين (رصيد سالب)"
        ])
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                min-width: 200px;
            }
            QComboBox:hover {
                border: 2px solid #2563eb;
                background: #f8fafc;
            }
        """)

        # إضافة العناصر للتخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(filter_label)
        search_layout.addWidget(self.status_filter)
        search_frame.setLayout(search_layout)

        # إنشاء الجدول
        self.create_suppliers_table()

        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addWidget(search_frame)
        main_layout.addWidget(self.suppliers_table, 1)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.status_filter.currentTextChanged.connect(self.on_filter_changed)

        # تحميل البيانات
        self.create_sample_data_if_empty()
        self.refresh_data()

    def create_suppliers_table(self):
        """إنشاء جدول الموردين مطابق للعملاء مع 9 أعمدة"""
        # إنشاء الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للعملاء (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "🚛 اسم المورد",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة المورد",
            "📋 الملاحظات",
            "🗓️ تاريخ الإضافة"
        ]

        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مطابقة للعملاء
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setAlternatingRowColors(False)
        self.suppliers_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة للعملاء
        self.suppliers_table.verticalHeader().setDefaultSectionSize(50)
        self.suppliers_table.verticalHeader().setVisible(False)

        header = self.suppliers_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إعداد عرض الأعمدة مطابق للعملاء (الترتيب الجديد: رقم، اسم، عنوان، بريد، هاتف، رصيد، حالة، ملاحظات، تاريخ)
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # الرقم - عرض ثابت
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # الاسم - عرض قابل للتعديل
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # العنوان - عرض قابل للتعديل
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # البريد - عرض قابل للتعديل
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # الهاتف - عرض ثابت
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # الرصيد - عرض ثابت
        header.setSectionResizeMode(6, QHeaderView.Interactive)  # الحالة - عرض ثابت
        header.setSectionResizeMode(7, QHeaderView.Interactive)  # الملاحظات - عرض قابل للتعديل
        header.setSectionResizeMode(8, QHeaderView.Interactive)  # التاريخ - عرض ثابت

        # تحديد عرض محدد للأعمدة - المقاسات الجديدة المحسنة مطابقة للعملاء
        header.resizeSection(0, 100)  # 🔢 ID - 100px
        header.resizeSection(1, 300)  # 🚛 اسم المورد - 300px
        header.resizeSection(2, 300)  # 🏠 العنوان - 300px
        header.resizeSection(3, 240)  # 📧 البريد الإلكتروني - 240px
        header.resizeSection(4, 170)  # 📱 رقم الهاتف - 170px
        header.resizeSection(5, 160)  # 💵 الرصيد - 160px
        header.resizeSection(6, 170)  # ⭐ حالة المورد - 170px
        header.resizeSection(7, 280)  # 📋 الملاحظات - 280px
        header.resizeSection(8, 180)  # 🗓️ تاريخ الإضافة - 180px

        # تطبيق التصميم والتفاعل مطابق للعملاء
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def on_filter_changed(self, text):
        """معالج تغيير التصفية"""
        try:
            if text == "نشط (رصيد موجب)":
                self.current_filter_value = "active"
            elif text == "عادي (رصيد صفر)":
                self.current_filter_value = "normal"
            elif text == "مدين (رصيد سالب)":
                self.current_filter_value = "debtor"
            else:
                self.current_filter_value = None

            # تطبيق التصفية
            self.filter_suppliers()
        except Exception as e:
            pass

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = self.current_filter_value

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية البيانات: {str(e)}")

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول مطابقة للفواتير"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.suppliers_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.suppliers_table.viewport())
            paint_watermark(painter, self.suppliers_table.viewport().rect())
            painter.end()

        self.suppliers_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول مطابق للفواتير"""
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.cellDoubleClicked.connect(self.edit_supplier)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.suppliers_table.itemAt(event.pos())
            if item is None:
                self.suppliers_table.clearSelection()
            QTableWidget.mousePressEvent(self.suppliers_table, event)

        self.suppliers_table.mousePressEvent = mousePressEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول مطابق للفواتير"""
        selected_items = self.suppliers_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        if hasattr(self, 'edit_button'):
            self.edit_button.setEnabled(has_selection)
        if hasattr(self, 'delete_button'):
            self.delete_button.setEnabled(has_selection)
        if hasattr(self, 'view_button'):
            self.view_button.setEnabled(has_selection)
        if hasattr(self, 'add_payment_button'):
            self.add_payment_button.setEnabled(has_selection)

    def edit_supplier(self):
        """تعديل بيانات مورد - دالة مؤقتة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"تعديل المورد رقم: {supplier_id}")
            # TODO: إضافة نافذة تعديل المورد
        except Exception as e:
            print(f"خطأ في تعديل المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """استخراج معرف المورد المحدد من الجدول مطابق للفواتير"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار مورد من القائمة"

            if not self.suppliers_table.item(selected_row, 0):
                return None, "الرجاء اختيار مورد صالح من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.suppliers_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم المورد"

            supplier_id = int(numbers[0])
            return supplier_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف المورد: {str(e)}"

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
            self.update_summary(suppliers)
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def update_summary(self, suppliers):
        """تحديث ملخص الموردين مطابق للفواتير"""
        try:
            # حساب الإحصائيات
            total_suppliers = len(suppliers)
            active_suppliers = len([s for s in suppliers if (s.balance or 0) > 0])
            debtor_suppliers = len([s for s in suppliers if (s.balance or 0) < 0])
            normal_suppliers = len([s for s in suppliers if (s.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(s.balance for s in suppliers if (s.balance or 0) > 0)
            total_negative = sum(abs(s.balance) for s in suppliers if (s.balance or 0) < 0)
            net_balance = sum(s.balance or 0 for s in suppliers)

            # تحديث النصوص (إذا كان هناك عنصر ملخص)
            if hasattr(self, 'summary_label'):
                summary_text = (f"إجمالي الموردين: {total_suppliers} | "
                              f"نشط: {active_suppliers} | "
                              f"مدين: {debtor_suppliers} | "
                              f"عادي: {normal_suppliers}")
                self.summary_label.setText(summary_text)

            if hasattr(self, 'balance_label'):
                balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                              f"الأرصدة السالبة: {format_currency(total_negative)} | "
                              f"صافي الرصيد: {format_currency(net_balance)}")
                self.balance_label.setText(balance_text)

            # تحديث عداد الموردين في الأزرار
            if hasattr(self, 'total_label'):
                self.total_label.setText(f"إجمالي الموردين: {total_suppliers}")

        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {str(e)}")

    def populate_table(self, suppliers):
        """ملء الجدول بالبيانات مطابق للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.suppliers_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.suppliers_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للفواتير
            for row, supplier in enumerate(suppliers):
                try:
                    self.suppliers_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة مطابق للفواتير
                    id_item = QTableWidgetItem(f"🆔 #{str(supplier.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🆔 الرقم التسلسلي: {supplier.id}")
                    self.suppliers_table.setItem(row, 0, id_item)

                    # 2. اسم المورد مع تنسيق احترافي
                    name_text = supplier.name if supplier.name and supplier.name.strip() else "No data"
                    name_item = QTableWidgetItem(f"🚛 {name_text}")
                    name_item.setTextAlignment(Qt.AlignCenter)
                    name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if name_text == "No data":
                        name_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        name_item.setForeground(QColor("#1e40af"))
                    name_item.setToolTip(f"🚛 اسم المورد: {name_text}\n💡 انقر نقرتين للتعديل")
                    self.suppliers_table.setItem(row, 1, name_item)

                    # 3. العنوان مع تنسيق جذاب
                    address_text = supplier.address if supplier.address and supplier.address.strip() else "No data"
                    address_item = QTableWidgetItem(f"📍 {address_text}")
                    address_item.setTextAlignment(Qt.AlignCenter)
                    address_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if address_text == "No data":
                        address_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        address_item.setForeground(QColor("#065f46"))
                    address_item.setToolTip(f"📍 العنوان: {address_text}")
                    self.suppliers_table.setItem(row, 2, address_item)

                    # 4. البريد الإلكتروني مع تنسيق أنيق
                    email_text = supplier.email if supplier.email and supplier.email.strip() else "No data"
                    email_item = QTableWidgetItem(f"📧 {email_text}")
                    email_item.setTextAlignment(Qt.AlignCenter)
                    email_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if email_text == "No data":
                        email_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        email_item.setForeground(QColor("#374151"))
                    email_item.setToolTip(f"📧 البريد الإلكتروني: {email_text}")
                    self.suppliers_table.setItem(row, 3, email_item)

                    # 5. رقم الهاتف مع تنسيق متطور
                    phone_text = supplier.phone if supplier.phone and supplier.phone.strip() else "No data"
                    phone_item = QTableWidgetItem(f"📞 {phone_text}")
                    phone_item.setTextAlignment(Qt.AlignCenter)
                    phone_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if phone_text == "No data":
                        phone_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        phone_item.setForeground(QColor("#374151"))
                    phone_item.setToolTip(f"📞 رقم الهاتف: {phone_text}")
                    self.suppliers_table.setItem(row, 4, phone_item)

                    # 6. الرصيد مع تنسيق مالي متطور مطابق للفواتير
                    try:
                        balance = supplier.balance or 0
                        if balance and balance != 0:
                            balance_formatted = f"{int(abs(balance)):,}".replace(',', '٬')
                            if balance > 0:
                                balance_display = f"💎 {balance_formatted} جنيه"
                                balance_color = QColor("#059669")  # أخضر للموجب
                            else:
                                balance_display = f"💸 {balance_formatted} جنيه"
                                balance_color = QColor("#dc2626")  # أحمر للسالب
                        else:
                            balance_display = "💰 0 جنيه"
                            balance_color = QColor("#6b7280")  # رمادي للصفر
                    except Exception:
                        balance_display = "💰 0 جنيه"
                        balance_color = QColor("#6b7280")

                    balance_item = QTableWidgetItem(balance_display)
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    balance_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    balance_item.setForeground(balance_color)
                    balance_item.setToolTip(f"💰 رصيد المورد: {balance_display}")
                    self.suppliers_table.setItem(row, 5, balance_item)

                    # 7. حالة المورد مع تصميم متطور ومؤشرات بصرية مطابق للفواتير
                    status_map = {
                        'active': {
                            'text': '🎉 نشط',
                            'color': QColor("#059669"),
                            'tooltip': '🎉 المورد نشط ولديه رصيد موجب\n✅ يمكن التعامل معه'
                        },
                        'normal': {
                            'text': '🎯 عادي',
                            'color': QColor("#d97706"),
                            'tooltip': '🎯 المورد برصيد صفر\n💡 حالة عادية'
                        },
                        'debtor': {
                            'text': '⚠️ مدين',
                            'color': QColor("#dc2626"),
                            'tooltip': '⚠️ المورد مدين برصيد سالب\n🔄 يتطلب متابعة'
                        }
                    }

                    # تحديد حالة المورد
                    if balance > 0:
                        status_key = 'active'
                    elif balance == 0:
                        status_key = 'normal'
                    else:
                        status_key = 'debtor'

                    status_info = status_map.get(status_key, {
                        'text': f"❓ غير محدد",
                        'color': QColor("#6b7280"),
                        'tooltip': f"❓ حالة غير معروفة"
                    })

                    status_item = QTableWidgetItem(status_info['text'])
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    status_item.setForeground(status_info['color'])
                    status_item.setToolTip(status_info['tooltip'])
                    self.suppliers_table.setItem(row, 6, status_item)

                    # 8. الملاحظات مع تنسيق أنيق
                    notes_text = supplier.notes if supplier.notes and supplier.notes.strip() else "No data"
                    notes_item = QTableWidgetItem(f"📋 {notes_text}")
                    notes_item.setTextAlignment(Qt.AlignCenter)
                    notes_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if notes_text == "No data":
                        notes_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        notes_item.setForeground(QColor("#374151"))
                    notes_item.setToolTip(f"📋 الملاحظات: {notes_text}")
                    self.suppliers_table.setItem(row, 7, notes_item)

                    # 9. تاريخ الإضافة مع تنسيق التاريخ
                    try:
                        if hasattr(supplier, 'created_at') and supplier.created_at:
                            date_formatted = supplier.created_at.strftime("%d/%m/%Y")
                            date_display = f"🗓️ {date_formatted}"
                        else:
                            date_display = "🗓️ غير محدد"
                    except Exception:
                        date_display = "🗓️ غير محدد"

                    date_item = QTableWidgetItem(date_display)
                    date_item.setTextAlignment(Qt.AlignCenter)
                    date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    date_item.setForeground(QColor("#374151"))
                    date_item.setToolTip(f"🗓️ تاريخ إضافة المورد: {date_display}")
                    self.suppliers_table.setItem(row, 8, date_item)

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.suppliers_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.suppliers_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول الموردين: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود موردين
            supplier_count = self.session.query(Supplier).count()

            if supplier_count == 0:
                # إنشاء موردين تجريبيين مع الحقول الجديدة
                import datetime
                sample_suppliers = [
                    Supplier(
                        name="شركة الأهرام للتوريدات",
                        address="القاهرة، مصر الجديدة",
                        phone="01234567890",
                        email="<EMAIL>",
                        balance=15000.0,
                        notes="مورد موثوق للمواد الخام والمعدات",
                        created_at=datetime.datetime.now() - datetime.timedelta(days=30)
                    ),
                    Supplier(
                        name="مؤسسة النيل التجارية",
                        address="الجيزة، المهندسين",
                        phone="01098765432",
                        email="<EMAIL>",
                        balance=-5000.0,
                        notes="يحتاج متابعة للمستحقات المالية",
                        created_at=datetime.datetime.now() - datetime.timedelta(days=25)
                    ),
                    Supplier(
                        name="شركة الدلتا للمواد",
                        address="الإسكندرية، سموحة",
                        phone="01555123456",
                        email="<EMAIL>",
                        balance=0.0,
                        notes="مورد جديد في فترة التقييم",
                        created_at=datetime.datetime.now() - datetime.timedelta(days=20)
                    ),
                    Supplier(
                        name="مجموعة الصعيد للتجارة",
                        address="أسوان، وسط البلد",
                        phone="01777888999",
                        email="<EMAIL>",
                        balance=25000.0,
                        notes="شريك استراتيجي - أولوية عالية",
                        created_at=datetime.datetime.now() - datetime.timedelta(days=15)
                    ),
                    Supplier(
                        name="شركة البحر الأحمر",
                        address="الغردقة، السقالة",
                        phone="01666555444",
                        email="<EMAIL>",
                        balance=-2500.0,
                        notes="مورد موسمي - يحتاج مراجعة الأسعار",
                        created_at=datetime.datetime.now() - datetime.timedelta(days=10)
                    )
                ]

                # إضافة الموردين لقاعدة البيانات
                for supplier in sample_suppliers:
                    self.session.add(supplier)

                # حفظ التغييرات
                self.session.commit()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()

    def create_action_buttons(self):
        """إنشاء إطار الأزرار مطابق للفواتير"""
        from PyQt5.QtWidgets import QSizePolicy, QMenu, QAction

        # إنشاء الإطار السفلي مطابق للفواتير
        self.buttons_frame = QFrame()
        self.buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 8px;
                max-height: 80px;
            }
        """)

        # إنشاء التخطيط العمودي للإطار السفلي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(0, 0, 0, 0)
        bottom_container.setSpacing(0)

        # إنشاء التخطيط الأفقي للأزرار
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(8, 8, 8, 8)
        actions_layout.setSpacing(8)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار النظيفة والمرتبة مطابق للفواتير

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة مورد")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_supplier)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_supplier)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_supplier)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')
        self.view_button.clicked.connect(self.view_supplier)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إضافة دفعة (مناسب للموردين)
        self.add_payment_button = QPushButton("💰 إضافة دفعة")
        self.style_advanced_button(self.add_payment_button, 'orange')
        self.add_payment_button.clicked.connect(self.add_payment)
        self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير مطابقة للفواتير
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: #ffffff;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background: #3b82f6;
                color: white;
            }
        """)

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        self.export_button.setMenu(export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي الموردين مطور ليتشابه مع الأزرار مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي الموردين: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        self.buttons_frame.setLayout(bottom_container)

        # تعطيل الأزرار التي تحتاج تحديد عنصر
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.view_button.setEnabled(False)
        self.add_payment_button.setEnabled(False)

    def style_advanced_button(self, button, color_scheme_name, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للفواتير"""
        # مخططات الألوان المتطورة مطابقة للفواتير
        color_schemes = {
            'emerald': {
                'bg_start': 'rgba(16, 185, 129, 0.9)',
                'bg_mid': 'rgba(34, 197, 94, 0.8)',
                'bg_end': 'rgba(52, 211, 153, 0.7)',
                'bg_bottom': 'rgba(6, 78, 59, 0.9)',
                'hover_start': 'rgba(34, 197, 94, 1.0)',
                'hover_mid': 'rgba(52, 211, 153, 0.9)',
                'hover_end': 'rgba(110, 231, 183, 0.8)',
                'hover_bottom': 'rgba(6, 95, 70, 1.0)',
                'pressed_start': 'rgba(5, 150, 105, 1.0)',
                'pressed_mid': 'rgba(16, 185, 129, 0.9)',
                'pressed_end': 'rgba(34, 197, 94, 0.8)',
                'pressed_bottom': 'rgba(4, 120, 87, 1.0)',
                'border': 'rgba(16, 185, 129, 0.8)',
                'shadow': 'rgba(16, 185, 129, 0.4)'
            },
            'info': {
                'bg_start': 'rgba(59, 130, 246, 0.9)',
                'bg_mid': 'rgba(96, 165, 250, 0.8)',
                'bg_end': 'rgba(147, 197, 253, 0.7)',
                'bg_bottom': 'rgba(30, 64, 175, 0.9)',
                'hover_start': 'rgba(96, 165, 250, 1.0)',
                'hover_mid': 'rgba(147, 197, 253, 0.9)',
                'hover_end': 'rgba(191, 219, 254, 0.8)',
                'hover_bottom': 'rgba(37, 99, 235, 1.0)',
                'pressed_start': 'rgba(37, 99, 235, 1.0)',
                'pressed_mid': 'rgba(59, 130, 246, 0.9)',
                'pressed_end': 'rgba(96, 165, 250, 0.8)',
                'pressed_bottom': 'rgba(29, 78, 216, 1.0)',
                'border': 'rgba(59, 130, 246, 0.8)',
                'shadow': 'rgba(59, 130, 246, 0.4)'
            },
            'danger': {
                'bg_start': 'rgba(239, 68, 68, 0.9)',
                'bg_mid': 'rgba(248, 113, 113, 0.8)',
                'bg_end': 'rgba(252, 165, 165, 0.7)',
                'bg_bottom': 'rgba(153, 27, 27, 0.9)',
                'hover_start': 'rgba(248, 113, 113, 1.0)',
                'hover_mid': 'rgba(252, 165, 165, 0.9)',
                'hover_end': 'rgba(254, 202, 202, 0.8)',
                'hover_bottom': 'rgba(185, 28, 28, 1.0)',
                'pressed_start': 'rgba(220, 38, 38, 1.0)',
                'pressed_mid': 'rgba(239, 68, 68, 0.9)',
                'pressed_end': 'rgba(248, 113, 113, 0.8)',
                'pressed_bottom': 'rgba(127, 29, 29, 1.0)',
                'border': 'rgba(239, 68, 68, 0.8)',
                'shadow': 'rgba(239, 68, 68, 0.4)'
            },
            'modern_teal': {
                'bg_start': 'rgba(20, 184, 166, 0.9)',
                'bg_mid': 'rgba(45, 212, 191, 0.8)',
                'bg_end': 'rgba(94, 234, 212, 0.7)',
                'bg_bottom': 'rgba(13, 148, 136, 0.9)',
                'hover_start': 'rgba(45, 212, 191, 1.0)',
                'hover_mid': 'rgba(94, 234, 212, 0.9)',
                'hover_end': 'rgba(153, 246, 228, 0.8)',
                'hover_bottom': 'rgba(15, 118, 110, 1.0)',
                'pressed_start': 'rgba(17, 94, 89, 1.0)',
                'pressed_mid': 'rgba(20, 184, 166, 0.9)',
                'pressed_end': 'rgba(45, 212, 191, 0.8)',
                'pressed_bottom': 'rgba(19, 78, 74, 1.0)',
                'border': 'rgba(20, 184, 166, 0.8)',
                'shadow': 'rgba(20, 184, 166, 0.4)'
            },
            'indigo': {
                'bg_start': 'rgba(99, 102, 241, 0.9)',
                'bg_mid': 'rgba(129, 140, 248, 0.8)',
                'bg_end': 'rgba(165, 180, 252, 0.7)',
                'bg_bottom': 'rgba(67, 56, 202, 0.9)',
                'hover_start': 'rgba(129, 140, 248, 1.0)',
                'hover_mid': 'rgba(165, 180, 252, 0.9)',
                'hover_end': 'rgba(196, 181, 253, 0.8)',
                'hover_bottom': 'rgba(79, 70, 229, 1.0)',
                'pressed_start': 'rgba(79, 70, 229, 1.0)',
                'pressed_mid': 'rgba(99, 102, 241, 0.9)',
                'pressed_end': 'rgba(129, 140, 248, 0.8)',
                'pressed_bottom': 'rgba(55, 48, 163, 1.0)',
                'border': 'rgba(99, 102, 241, 0.8)',
                'shadow': 'rgba(99, 102, 241, 0.4)'
            },
            'orange': {
                'bg_start': 'rgba(249, 115, 22, 0.9)',
                'bg_mid': 'rgba(251, 146, 60, 0.8)',
                'bg_end': 'rgba(253, 186, 116, 0.7)',
                'bg_bottom': 'rgba(194, 65, 12, 0.9)',
                'hover_start': 'rgba(251, 146, 60, 1.0)',
                'hover_mid': 'rgba(253, 186, 116, 0.9)',
                'hover_end': 'rgba(254, 215, 170, 0.8)',
                'hover_bottom': 'rgba(234, 88, 12, 1.0)',
                'pressed_start': 'rgba(154, 52, 18, 1.0)',
                'pressed_mid': 'rgba(249, 115, 22, 0.9)',
                'pressed_end': 'rgba(251, 146, 60, 0.8)',
                'pressed_bottom': 'rgba(124, 45, 18, 1.0)',
                'border': 'rgba(249, 115, 22, 0.8)',
                'shadow': 'rgba(249, 115, 22, 0.4)'
            },
            'rose': {
                'bg_start': 'rgba(244, 63, 94, 0.9)',
                'bg_mid': 'rgba(251, 113, 133, 0.8)',
                'bg_end': 'rgba(252, 165, 165, 0.7)',
                'bg_bottom': 'rgba(190, 18, 60, 0.9)',
                'hover_start': 'rgba(251, 113, 133, 1.0)',
                'hover_mid': 'rgba(252, 165, 165, 0.9)',
                'hover_end': 'rgba(254, 205, 211, 0.8)',
                'hover_bottom': 'rgba(225, 29, 72, 1.0)',
                'pressed_start': 'rgba(159, 18, 57, 1.0)',
                'pressed_mid': 'rgba(244, 63, 94, 0.9)',
                'pressed_end': 'rgba(251, 113, 133, 0.8)',
                'pressed_bottom': 'rgba(136, 19, 55, 1.0)',
                'border': 'rgba(244, 63, 94, 0.8)',
                'shadow': 'rgba(244, 63, 94, 0.4)'
            }
        }

        color_scheme = color_schemes.get(color_scheme_name, color_schemes['info'])

        # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
        menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

        # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
        style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['bg_start']},
                    stop:0.15 {color_scheme['bg_mid']},
                    stop:0.85 {color_scheme['bg_end']},
                    stop:1 {color_scheme['bg_bottom']});
                color: #ffffff;
                border: 3px solid {color_scheme['border']};
                border-radius: 12px;
                padding: 10px 16px;
                font-size: 13px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-align: center;
                min-height: 35px;
                max-height: 45px;
                box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                           0 0 20px {color_scheme['shadow']};
                letter-spacing: 1px;
                text-transform: uppercase;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['hover_start']},
                    stop:0.15 {color_scheme['hover_mid']},
                    stop:0.85 {color_scheme['hover_end']},
                    stop:1 {color_scheme['hover_bottom']});
                border: 4px solid {color_scheme['border']};
                transform: translateY(-2px);
                box-shadow: inset 0 2px 0 rgba(255, 255, 255, 0.5),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 30px {color_scheme['shadow']};
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           1px 1px 3px rgba(0, 0, 0, 0.7);
                letter-spacing: 1.2px;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['pressed_start']},
                    stop:0.15 {color_scheme['pressed_mid']},
                    stop:0.85 {color_scheme['pressed_end']},
                    stop:1 {color_scheme['pressed_bottom']});
                border: 3px solid {color_scheme['border']};
                transform: translateY(1px);
                box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.5),
                           inset 0 0 15px rgba(0, 0, 0, 0.4);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                           1px 1px 2px rgba(0, 0, 0, 0.8);
                letter-spacing: 0.8px;
            }}
            QPushButton:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                color: #d1d5db;
                border: 3px solid #6b7280;
                box-shadow: none;
                text-shadow: none;
                opacity: 0.6;
            }}
            {menu_indicator}
        """

        button.setStyleSheet(style)

    # ==================== دوال معالجة أحداث الأزرار ====================

    def add_supplier(self):
        """إضافة مورد جديد - دالة مؤقتة"""
        print("➕ إضافة مورد جديد")
        # TODO: إضافة نافذة إضافة مورد

    def delete_supplier(self):
        """حذف مورد - دالة مؤقتة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"🗑️ حذف المورد رقم: {supplier_id}")
            # TODO: إضافة تأكيد الحذف وحذف المورد
        except Exception as e:
            print(f"خطأ في حذف المورد: {str(e)}")

    def view_supplier(self):
        """عرض تفاصيل مورد - دالة مؤقتة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"👁️ عرض تفاصيل المورد رقم: {supplier_id}")
            # TODO: إضافة نافذة عرض تفاصيل المورد
        except Exception as e:
            print(f"خطأ في عرض تفاصيل المورد: {str(e)}")

    def add_payment(self):
        """إضافة دفعة للمورد - دالة مؤقتة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"💰 إضافة دفعة للمورد رقم: {supplier_id}")
            # TODO: إضافة نافذة إضافة دفعة
        except Exception as e:
            print(f"خطأ في إضافة دفعة: {str(e)}")

    def export_to_excel(self):
        """تصدير إلى Excel - دالة مؤقتة"""
        print("📊 تصدير إلى Excel")
        # TODO: إضافة تصدير Excel

    def export_to_csv(self):
        """تصدير إلى CSV - دالة مؤقتة"""
        print("📄 تصدير إلى CSV")
        # TODO: إضافة تصدير CSV

    def show_statistics(self):
        """عرض الإحصائيات - دالة مؤقتة"""
        print("📊 عرض الإحصائيات")
        # TODO: إضافة نافذة الإحصائيات