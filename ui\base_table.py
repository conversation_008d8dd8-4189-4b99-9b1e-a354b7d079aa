from PyQt5.QtWidgets import (QWidget, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter
import re

class BaseTableWidget(QWidget):
    """فئة أساسية موحدة للجداول - قابلة للنسخ 100%"""
    
    # إشارات للتفاعل مع الجدول
    item_selected = pyqtSignal(int)  # عند تحديد عنصر
    item_double_clicked = pyqtSignal(int)  # عند النقر المزدوج
    refresh_requested = pyqtSignal()  # عند طلب التحديث
    
    def __init__(self, session, section_name="القسم", section_icon="📋"):
        super().__init__()
        self.session = session
        self.section_name = section_name
        self.section_icon = section_icon
        self.table = None
        self.search_edit = None
        self.status_filter = None
        self.current_filter_value = None
        self.current_filter_label = None
        
        # أزرار القسم
        self.add_button = None
        self.edit_button = None
        self.delete_button = None
        self.refresh_button = None
        self.view_button = None
        self.export_button = None
        self.statistics_button = None
        
    def create_unified_layout(self, columns_config, filter_options=None):
        """إنشاء التخطيط الموحد للجدول"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)
        
        # إضافة العنوان الرئيسي
        title_label = self.create_unified_title()
        main_layout.addWidget(title_label)
        
        # إنشاء الإطار العلوي للبحث والتصفية
        top_frame = self.create_unified_search_frame()
        main_layout.addWidget(top_frame)
        
        # إنشاء الجدول الموحد
        self.create_unified_table(columns_config)
        main_layout.addWidget(self.table, 1)
        
        # إنشاء الإطار السفلي للأزرار
        bottom_frame = self.create_unified_buttons_frame()
        main_layout.addWidget(bottom_frame)
        
        self.setLayout(main_layout)
        
        # تهيئة حالة الأزرار
        self.initialize_unified_button_states()
        
    def create_unified_title(self):
        """إنشاء العنوان الموحد"""
        title_text = f"{self.section_icon} إدارة {self.section_name} المتطورة - نظام شامل ومتقدم لإدارة {self.section_name} مع أدوات احترافية للبحث والتحليل والتقارير"
        title_label = QLabel(title_text)
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        return title_label
        
    def create_unified_search_frame(self):
        """إنشاء إطار البحث والتصفية الموحد"""
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        
        # تخطيط أفقي للبحث
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(4)
        
        # حاوي عمودي للتوسيط
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)
        top_container.setSpacing(0)
        top_container.addStretch(1)
        top_container.addLayout(search_layout)
        top_container.addStretch(1)
        
        # تسمية البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet(self.get_unified_label_style())
        
        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(f"ابحث في {self.section_name}...")
        self.search_edit.setStyleSheet(self.get_unified_input_style())
        self.search_edit.textChanged.connect(self.on_search_changed)
        
        # زر البحث
        search_button = QPushButton("🔍")
        search_button.setStyleSheet(self.get_unified_search_button_style())
        search_button.clicked.connect(self.on_search_clicked)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        
        # تسمية التصفية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet(self.get_unified_label_style())
        filter_label.setAlignment(Qt.AlignCenter)
        
        # إنشاء قائمة التصفية المخصصة
        self.create_unified_status_filter()
        
        # إضافة العناصر للتخطيط
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)
        
        top_frame.setLayout(top_container)
        return top_frame
        
    def create_unified_table(self, columns_config):
        """إنشاء الجدول الموحد"""
        self.table = QTableWidget()
        self.table.setColumnCount(len(columns_config))
        
        # تعيين عناوين الأعمدة
        headers = [col['header'] for col in columns_config]
        self.table.setHorizontalHeaderLabels(headers)
        
        # إعداد خصائص الجدول
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        # تعيين عرض الأعمدة
        header = self.table.horizontalHeader()
        for i, col in enumerate(columns_config):
            if 'width' in col:
                header.resizeSection(i, col['width'])
            if col.get('stretch', False):
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.Interactive)
        
        # تطبيق التصميم الموحد
        self.apply_unified_table_style()
        self.add_unified_watermark()
        self.setup_unified_table_interactions()
        
    def apply_unified_table_style(self):
        """تطبيق التصميم الموحد للجدول"""
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
        """)

    def add_unified_watermark(self):
        """إضافة العلامة المائية الموحدة"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.table.viewport())
                paint_watermark(painter, self.table.viewport().rect())
                painter.end()
            except Exception as e:
                pass  # خطأ في رسم العلامة المائية

        self.table.paintEvent = new_paint_event

    def setup_unified_table_interactions(self):
        """إعداد التفاعلات الموحدة مع الجدول"""
        self.table.itemSelectionChanged.connect(self.on_unified_selection_changed)
        self.table.cellDoubleClicked.connect(self.on_unified_cell_double_clicked)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            try:
                item = self.table.itemAt(event.pos())
                if item is None:
                    self.table.clearSelection()
                QTableWidget.mousePressEvent(self.table, event)
            except Exception as e:
                pass  # خطأ في mousePressEvent

        self.table.mousePressEvent = mousePressEvent

    def create_unified_buttons_frame(self):
        """إنشاء إطار الأزرار الموحد"""
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # إنشاء الأزرار الموحدة
        self.create_unified_buttons(actions_layout)

        # حاوي عمودي للتوسيط
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)
        bottom_container.addStretch(1)
        bottom_container.addLayout(actions_layout)
        bottom_container.addStretch(1)

        bottom_frame.setLayout(bottom_container)
        return bottom_frame

    def create_unified_buttons(self, layout):
        """إنشاء الأزرار الموحدة"""
        # زر الإضافة
        self.add_button = QPushButton(f"➕ إضافة {self.section_name}")
        self.style_unified_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.on_add_clicked)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_unified_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.on_edit_clicked)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_unified_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.on_delete_clicked)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_unified_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.on_refresh_clicked)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_unified_button(self.view_button, 'cyan')
        self.view_button.clicked.connect(self.on_view_clicked)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_unified_button(self.export_button, 'orange', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.create_unified_export_menu()

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_unified_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.on_statistics_clicked)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        layout.addWidget(self.add_button)
        layout.addWidget(self.edit_button)
        layout.addWidget(self.delete_button)
        layout.addWidget(self.refresh_button)
        layout.addWidget(self.view_button)
        layout.addWidget(self.export_button)
        layout.addWidget(self.statistics_button)

    def create_unified_export_menu(self):
        """إنشاء قائمة التصدير الموحدة"""
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(234, 88, 12, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                color: #1f2937;
                font-weight: 700;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(234, 88, 12, 0.2),
                    stop:0.5 rgba(249, 115, 22, 0.15),
                    stop:1 rgba(251, 146, 60, 0.2));
                color: #1e40af;
                border: 2px solid rgba(234, 88, 12, 0.4);
                font-weight: 900;
            }
        """)

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.on_export_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.on_export_csv)
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF", self)
        pdf_action.triggered.connect(self.on_export_pdf)
        export_menu.addAction(pdf_action)

        self.export_button.setMenu(export_menu)

    def create_unified_status_filter(self):
        """إنشاء قائمة التصفية الموحدة"""
        self.status_filter = QFrame()
        self.status_filter.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        # تخطيط أفقي للإطار
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(0)

        # تسمية الحالة الحالية
        self.current_filter_label = QLabel("🌟 جميع البيانات")
        self.current_filter_label.setStyleSheet("""
            QLabel {
                background: transparent;
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                padding: 0px;
                margin: 0px;
                text-align: center;
            }
        """)
        self.current_filter_label.setAlignment(Qt.AlignCenter)

        # سهم القائمة
        arrow_button = QPushButton("▼")
        arrow_button.setStyleSheet(self.get_unified_arrow_style())
        arrow_button.clicked.connect(self.show_filter_menu)

        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(arrow_button, 0)

        self.status_filter.setLayout(filter_layout)

        # تهيئة قيمة التصفية الافتراضية
        self.current_filter_value = None

    def get_unified_label_style(self):
        """الحصول على تنسيق التسميات الموحد"""
        return """
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """

    def get_unified_input_style(self):
        """الحصول على تنسيق حقول الإدخال الموحد"""
        return """
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                min-height: 33px;
                max-height: 37px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """

    def get_unified_search_button_style(self):
        """الحصول على تنسيق زر البحث الموحد"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 8px 12px;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """

    def get_unified_arrow_style(self):
        """الحصول على تنسيق السهم الموحد"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
        """

    def style_unified_button(self, button, color_theme, has_menu=False):
        """تطبيق التنسيق الموحد المتقدم للأزرار مطابق للأقسام الأخرى"""
        # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للأقسام الأخرى
        colors = {
            'emerald': {
                'bg_start': '#10b981', 'bg_mid': '#059669', 'bg_end': '#047857', 'bg_bottom': '#065f46',
                'hover_start': '#34d399', 'hover_mid': '#10b981', 'hover_end': '#059669', 'hover_bottom': '#047857',
                'hover_border': '#10b981', 'pressed_start': '#047857', 'pressed_mid': '#065f46',
                'pressed_end': '#064e3b', 'pressed_bottom': '#022c22', 'pressed_border': '#059669',
                'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
            },
            'info': {
                'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
            },
            'danger': {
                'bg_start': '#dc2626', 'bg_mid': '#b91c1c', 'bg_end': '#991b1b', 'bg_bottom': '#7f1d1d',
                'hover_start': '#ef4444', 'hover_mid': '#dc2626', 'hover_end': '#b91c1c', 'hover_bottom': '#991b1b',
                'hover_border': '#dc2626', 'pressed_start': '#991b1b', 'pressed_mid': '#7f1d1d',
                'pressed_end': '#6b1d1d', 'pressed_bottom': '#450a0a', 'pressed_border': '#b91c1c',
                'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
            },
            'modern_teal': {
                'bg_start': '#14b8a6', 'bg_mid': '#0d9488', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a',
                'hover_start': '#2dd4bf', 'hover_mid': '#14b8a6', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e',
                'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#134e4a',
                'pressed_end': '#115e59', 'pressed_bottom': '#042f2e', 'pressed_border': '#0d9488',
                'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
            },
            'cyan': {
                'bg_start': '#06b6d4', 'bg_mid': '#0891b2', 'bg_end': '#0e7490', 'bg_bottom': '#155e75',
                'hover_start': '#22d3ee', 'hover_mid': '#06b6d4', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490',
                'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#155e75',
                'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0891b2',
                'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
            },
            'orange': {
                'bg_start': '#ea580c', 'bg_mid': '#c2410c', 'bg_end': '#9a3412', 'bg_bottom': '#7c2d12',
                'hover_start': '#fb923c', 'hover_mid': '#ea580c', 'hover_end': '#c2410c', 'hover_bottom': '#9a3412',
                'hover_border': '#ea580c', 'pressed_start': '#9a3412', 'pressed_mid': '#7c2d12',
                'pressed_end': '#6c2e12', 'pressed_bottom': '#431407', 'pressed_border': '#c2410c',
                'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.5)'
            },
            'rose': {
                'bg_start': '#f43f5e', 'bg_mid': '#e11d48', 'bg_end': '#be123c', 'bg_bottom': '#9f1239',
                'hover_start': '#fb7185', 'hover_mid': '#f43f5e', 'hover_end': '#e11d48', 'hover_bottom': '#be123c',
                'hover_border': '#f43f5e', 'pressed_start': '#be123c', 'pressed_mid': '#9f1239',
                'pressed_end': '#881337', 'pressed_bottom': '#4c0519', 'pressed_border': '#e11d48',
                'border': '#f43f5e', 'text': '#ffffff', 'shadow': 'rgba(244, 63, 94, 0.5)'
            }
        }

        color_scheme = colors.get(color_theme, colors['info'])

        # تطبيق التصميم المتقدم مطابق للأقسام الأخرى
        style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['bg_start']},
                    stop:0.15 {color_scheme['bg_mid']},
                    stop:0.85 {color_scheme['bg_end']},
                    stop:1 {color_scheme['bg_bottom']});
                border: 4px solid {color_scheme['border']};
                border-radius: 20px;
                color: {color_scheme['text']};
                font-size: 16px;
                font-weight: 900;
                padding: 12px 20px;
                min-height: 35px;
                max-height: 45px;
                box-shadow: 0 6px 15px {color_scheme['shadow']},
                           inset 0 2px 0 rgba(255, 255, 255, 0.3),
                           inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                           0 0 20px {color_scheme['shadow']};
                letter-spacing: 1px;
                text-transform: uppercase;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                           1px 1px 2px rgba(0, 0, 0, 0.6);
                transition: all 0.3s ease;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['hover_start']},
                    stop:0.15 {color_scheme['hover_mid']},
                    stop:0.85 {color_scheme['hover_end']},
                    stop:1 {color_scheme['hover_bottom']});
                border: 5px solid {color_scheme['hover_border']};
                transform: translateY(-3px) scale(1.02);
                box-shadow: 0 10px 25px {color_scheme['shadow']},
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 30px {color_scheme['shadow']};
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           1px 1px 3px rgba(0, 0, 0, 0.7);
                letter-spacing: 1.2px;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color_scheme['pressed_start']},
                    stop:0.15 {color_scheme['pressed_mid']},
                    stop:0.85 {color_scheme['pressed_end']},
                    stop:1 {color_scheme['pressed_bottom']});
                border: 4px solid {color_scheme['pressed_border']};
                transform: translateY(2px) scale(0.98);
                box-shadow: 0 2px 8px {color_scheme['shadow']},
                           inset 0 2px 0 rgba(0, 0, 0, 0.4),
                           inset 0 -1px 0 rgba(255, 255, 255, 0.2),
                           0 0 15px {color_scheme['shadow']};
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
                letter-spacing: 0.8px;
            }}
            QPushButton:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(156, 163, 175, 0.5),
                    stop:0.15 rgba(107, 114, 128, 0.5),
                    stop:0.85 rgba(75, 85, 99, 0.5),
                    stop:1 rgba(55, 65, 81, 0.5));
                border: 2px solid rgba(156, 163, 175, 0.3);
                color: rgba(255, 255, 255, 0.5);
                box-shadow: none;
                text-shadow: none;
                transform: none;
            }}
        """

        button.setStyleSheet(style)

    def initialize_unified_button_states(self):
        """تهيئة حالة الأزرار الموحدة"""
        try:
            buttons = [
                self.add_button, self.edit_button, self.delete_button,
                self.refresh_button, self.view_button, self.export_button,
                self.statistics_button
            ]

            for button in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية
                    current_style = button.styleSheet()
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    bright_style = clean_style + "\nQPushButton { opacity: 1.0 !important; }"
                    button.setStyleSheet(bright_style)
                    button.show()
        except Exception as e:
            pass  # خطأ في تهيئة الأزرار

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    # معالجات الأحداث الموحدة
    def on_unified_selection_changed(self):
        """معالج تغيير التحديد الموحد"""
        selected_id = self.get_selected_item_id()
        if selected_id:
            self.item_selected.emit(selected_id)

    def on_unified_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج الموحد"""
        selected_id = self.get_selected_item_id()
        if selected_id:
            self.item_double_clicked.emit(selected_id)

    def on_search_changed(self):
        """معالج تغيير نص البحث"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_search_clicked(self):
        """معالج النقر على زر البحث"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    # معالجات الأزرار الموحدة
    def on_add_clicked(self):
        """معالج زر الإضافة"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_edit_clicked(self):
        """معالج زر التعديل"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_delete_clicked(self):
        """معالج زر الحذف"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_refresh_clicked(self):
        """معالج زر التحديث"""
        self.refresh_requested.emit()

    def on_view_clicked(self):
        """معالج زر عرض التفاصيل"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_statistics_clicked(self):
        """معالج زر الإحصائيات"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    # معالجات التصدير الموحدة
    def on_export_excel(self):
        """معالج تصدير Excel"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_export_csv(self):
        """معالج تصدير CSV"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    def on_export_pdf(self):
        """معالج تصدير PDF"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass

    # وظائف مساعدة موحدة
    def get_selected_item_id(self):
        """الحصول على معرف العنصر المحدد"""
        try:
            current_row = self.table.currentRow()
            if current_row >= 0:
                id_item = self.table.item(current_row, 0)
                if id_item:
                    # استخراج الرقم من النص الذي يحتوي على أيقونة
                    text = id_item.text()
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        return int(numbers[0])
            return None
        except Exception as e:
            return None

    def populate_unified_table(self, data, columns_config):
        """ملء الجدول بالبيانات الموحدة"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.table.setUpdatesEnabled(False)

            # مسح الجدول
            self.table.setRowCount(0)

            # إضافة الصفوف
            for row, item in enumerate(data):
                self.table.insertRow(row)

                # ملء الأعمدة حسب التكوين
                for col, config in enumerate(columns_config):
                    cell_data = self.get_cell_data(item, config)
                    cell_item = QTableWidgetItem(cell_data)
                    cell_item.setTextAlignment(Qt.AlignCenter)

                    # تطبيق تنسيق خاص إذا كانت البيانات فارغة
                    if cell_data == "No data":
                        cell_item.setForeground(QColor("#ef4444"))  # أحمر

                    self.table.setItem(row, col, cell_item)

            # إعادة تفعيل تحديث الجدول
            self.table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل التحديث في حالة الخطأ
            self.table.setUpdatesEnabled(True)

    def get_cell_data(self, item, config):
        """الحصول على بيانات الخلية حسب التكوين"""
        try:
            field_name = config.get('field', '')
            icon = config.get('icon', '')

            # الحصول على القيمة من العنصر
            if hasattr(item, field_name):
                value = getattr(item, field_name)
                if value is None or (isinstance(value, str) and not value.strip()):
                    return "No data"

                # تطبيق تنسيق خاص حسب نوع البيانات
                if config.get('type') == 'currency':
                    from utils import format_currency
                    return f"{icon} {format_currency(value)}"
                elif config.get('type') == 'date':
                    if hasattr(value, 'strftime'):
                        return f"{icon} {value.strftime('%Y-%m-%d')}"
                    return f"{icon} {value}"
                else:
                    return f"{icon} {value}"
            else:
                return "No data"

        except Exception as e:
            return "No data"

    def clear_unified_table(self):
        """مسح الجدول الموحد"""
        try:
            self.table.setRowCount(0)
        except Exception as e:
            pass

    def refresh_unified_table(self):
        """تحديث الجدول الموحد"""
        # يجب تنفيذ هذه الوظيفة في الفئات المشتقة
        pass
