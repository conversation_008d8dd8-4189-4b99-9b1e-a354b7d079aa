from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter
import re

from database import Employee
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # إنشاء التبويبات مع تصميم مطابق للإشعارات
        self.tabs = QTabWidget()
        # تعيين موضع التبويبات في الأعلى (الافتراضي)
        self.tabs.setTabPosition(QTabWidget.North)

        # إضافة عنوان ديناميكي يتغير حسب التبويبة المحددة
        self.title_label = QLabel("👷‍♂️ إدارة بيانات وحسابات العمال - نظام شامل ومتقدم لإدارة الموظفين والمعلومات الشخصية")
        self.title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        # تحسين تصميم التبويبات مع إطار أسود وعرض أكبر مطابق للإشعارات
        self.tabs.setStyleSheet("""
            QTabWidget {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #f8fafc;
                font-size: 14px;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #ffffff;
                top: -3px;
            }
            QTabBar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 30px;
                padding: 8px 32px;
                margin: 2px;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                font-weight: bold;
                font-size: 20px;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                border-radius: 12px;
                margin-top: -1px;
                padding: 9px 32px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                padding: 8px 32px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3),
                           0 2px 8px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إنشاء التبويبات
        self.create_employees_tab()
        self.create_daily_wages_tab()

        # ربط تغيير التبويبة بتحديث العنوان
        self.tabs.currentChanged.connect(self.update_title)

        # إضافة العناصر للتخطيط بالترتيب الصحيح
        main_layout.addWidget(self.tabs)  # التبويبات أولاً
        main_layout.addWidget(self.title_label)  # العنوان تحت التبويبات مباشرة
        self.setLayout(main_layout)

    def create_employees_tab(self):
        """إنشاء تبويب إدارة حسابات وبيانات العمال"""
        employees_widget = QWidget()
        layout = QVBoxLayout()

        # إطار البحث
        search_frame = QFrame()
        search_frame.setFixedHeight(55)
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 2px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 5, 10, 5)

        # تسمية البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #1e293b; font-weight: bold;")
        search_layout.addWidget(search_label)

        # حقل البحث
        self.employees_search_edit = QLineEdit()
        self.employees_search_edit.setPlaceholderText("البحث في بيانات العمال...")
        self.employees_search_edit.setFixedHeight(30)
        self.employees_search_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #000000;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 12px;
                background: #ffffff;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                background: #f0f9ff;
            }
        """)
        self.employees_search_edit.textChanged.connect(self.filter_employees)
        search_layout.addWidget(self.employees_search_edit)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # إضافة قائمة التصفية
        filter_frame = QFrame()
        filter_frame.setFixedHeight(55)
        filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 2px;
            }
        """)

        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(10, 5, 10, 5)

        # تسمية التصفية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        filter_label.setStyleSheet("color: #1e293b; font-weight: bold;")
        filter_layout.addWidget(filter_label)

        # قائمة التصفية
        self.employees_status_filter = QComboBox()
        self.employees_status_filter.addItems([
            "جميع الحالات",
            "نشط (رصيد موجب)",
            "عادي (رصيد صفر)",
            "مدين (رصيد سالب)"
        ])
        self.employees_status_filter.setFixedHeight(30)
        self.employees_status_filter.setStyleSheet("""
            QComboBox {
                border: 2px solid #000000;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 12px;
                background: #ffffff;
                color: #1e293b;
                min-width: 200px;
            }
            QComboBox:hover {
                border-color: #3b82f6;
                background: #f0f9ff;
            }
        """)
        self.employees_status_filter.currentTextChanged.connect(self.on_employees_filter_changed)
        filter_layout.addWidget(self.employees_status_filter)
        filter_layout.addStretch()

        filter_frame.setLayout(filter_layout)
        layout.addWidget(filter_frame)

        # إنشاء جدول العمال
        self.create_employees_table()
        layout.addWidget(self.employees_table)

        employees_widget.setLayout(layout)
        self.tabs.addTab(employees_widget, "👥 إدارة بيانات وحسابات العمال")

        # تهيئة المتغيرات
        self.current_employees_filter_value = None

        # تحميل البيانات
        self.create_employees_sample_data_if_empty()
        self.refresh_employees_data()

    def create_daily_wages_tab(self):
        """إنشاء تبويب إدارة الأجور اليومية"""
        wages_widget = QWidget()
        layout = QVBoxLayout()

        # إطار البحث
        search_frame = QFrame()
        search_frame.setFixedHeight(55)
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 2px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 5, 10, 5)

        # تسمية البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #1e293b; font-weight: bold;")
        search_layout.addWidget(search_label)

        # حقل البحث
        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("البحث في الأجور اليومية...")
        self.daily_wages_search_edit.setFixedHeight(30)
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #000000;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 12px;
                background: #ffffff;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                background: #f0f9ff;
            }
        """)
        self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
        search_layout.addWidget(self.daily_wages_search_edit)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # رسالة مؤقتة
        message_label = QLabel("💰 سيتم إضافة جدول الأجور اليومية وإدارة الرواتب قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: #f8fafc;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(message_label)

        wages_widget.setLayout(layout)
        self.tabs.addTab(wages_widget, "💰 إدارة الأجور اليومية للعمال")

    def filter_employees(self):
        """تصفية العمال - وظيفة مؤقتة"""
        print("🔍 البحث في العمال:", self.employees_search_edit.text())

    def filter_daily_wages(self):
        """تصفية الأجور اليومية - وظيفة مؤقتة"""
        print("🔍 البحث في الأجور اليومية:", self.daily_wages_search_edit.text())

    def save_daily_wages_data(self):
        """حفظ بيانات الأجور اليومية - وظيفة مؤقتة"""
        print("💾 حفظ بيانات الأجور اليومية")

    def update_title(self, index):
        """تحديث العنوان حسب التبويبة المحددة"""
        try:
            if index == 0:  # تبويبة إدارة البيانات
                self.title_label.setText("👥 إدارة بيانات وحسابات العمال - نظام شامل ومتقدم لإدارة الموظفين والمعلومات الشخصية والوظيفية")
            elif index == 1:  # تبويبة الأجور
                self.title_label.setText("💰 إدارة الأجور اليومية للعمال - نظام متكامل ومتطور للمحاسبة والتقارير المالية والرواتب")
        except Exception as e:
            pass  # خطأ في تحديث العنوان

    def refresh_data(self):
        """تحديث البيانات - وظيفة مؤقتة"""
        print("🔄 تحديث بيانات العمال")

    # ==================== دوال جدول العمال ====================

    def create_employees_table(self):
        """إنشاء جدول العمال مطابق للموردين مع 9 أعمدة"""
        # إنشاء الجدول
        self.employees_table = QTableWidget()
        self.employees_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للموردين (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "👷‍♂️ اسم العامل",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة العامل",
            "📋 الملاحظات",
            "🗓️ تاريخ التوظيف"
        ]

        self.employees_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مطابقة للموردين
        self.employees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.employees_table.setSelectionMode(QTableWidget.SingleSelection)
        self.employees_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.employees_table.setAlternatingRowColors(False)
        self.employees_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة للموردين
        self.employees_table.verticalHeader().setDefaultSectionSize(50)
        self.employees_table.verticalHeader().setVisible(False)

        header = self.employees_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إعداد عرض الأعمدة مطابق للموردين (الترتيب الجديد: رقم، اسم، عنوان، بريد، هاتف، رصيد، حالة، ملاحظات، تاريخ)
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # الرقم - عرض ثابت
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # الاسم - عرض قابل للتعديل
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # العنوان - عرض قابل للتعديل
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # البريد - عرض قابل للتعديل
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # الهاتف - عرض ثابت
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # الرصيد - عرض ثابت
        header.setSectionResizeMode(6, QHeaderView.Interactive)  # الحالة - عرض ثابت
        header.setSectionResizeMode(7, QHeaderView.Interactive)  # الملاحظات - عرض قابل للتعديل
        header.setSectionResizeMode(8, QHeaderView.Interactive)  # التاريخ - عرض ثابت

        # تحديد عرض محدد للأعمدة - المقاسات الجديدة المحسنة مطابقة للموردين
        header.resizeSection(0, 100)  # 🔢 ID - 100px
        header.resizeSection(1, 300)  # 👷‍♂️ اسم العامل - 300px
        header.resizeSection(2, 300)  # 🏠 العنوان - 300px
        header.resizeSection(3, 240)  # 📧 البريد الإلكتروني - 240px
        header.resizeSection(4, 170)  # 📱 رقم الهاتف - 170px
        header.resizeSection(5, 160)  # 💵 الرصيد - 160px
        header.resizeSection(6, 170)  # ⭐ حالة العامل - 170px
        header.resizeSection(7, 280)  # 📋 الملاحظات - 280px
        header.resizeSection(8, 180)  # 🗓️ تاريخ التوظيف - 180px

        # تطبيق التصميم والتفاعل مطابق للموردين
        self.apply_employees_table_style()
        self.add_employees_watermark_to_table()
        self.setup_employees_table_interactions()

    def on_employees_filter_changed(self, text):
        """معالج تغيير التصفية للعمال"""
        try:
            if text == "نشط (رصيد موجب)":
                self.current_employees_filter_value = "active"
            elif text == "عادي (رصيد صفر)":
                self.current_employees_filter_value = "normal"
            elif text == "مدين (رصيد سالب)":
                self.current_employees_filter_value = "debtor"
            else:
                self.current_employees_filter_value = None

            # تطبيق التصفية
            self.filter_employees()
        except Exception as e:
            pass

    def filter_employees(self):
        """تصفية العمال بناءً على نص البحث والحالة"""
        try:
            search_text = self.employees_search_edit.text().strip().lower()
            status = self.current_employees_filter_value

            # بناء الاستعلام
            query = self.session.query(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Employee.name.like(f"%{search_text}%") |
                    Employee.phone.like(f"%{search_text}%") |
                    Employee.email.like(f"%{search_text}%") |
                    Employee.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Employee.balance > 0)
            elif status == "normal":
                query = query.filter(Employee.balance == 0)
            elif status == "debtor":
                query = query.filter(Employee.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            employees = query.order_by(Employee.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_employees_table(employees)
            self.update_employees_summary(employees)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية بيانات العمال: {str(e)}")

    def apply_employees_table_style(self):
        """تطبيق التصميم المتطور لجدول العمال مطابق للموردين"""
        self.employees_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_employees_watermark_to_table(self):
        """إضافة علامة مائية لجدول العمال مطابقة للموردين"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.employees_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.employees_table.viewport())
            paint_watermark(painter, self.employees_table.viewport().rect())
            painter.end()

        self.employees_table.paintEvent = new_paint_event

    def setup_employees_table_interactions(self):
        """إعداد التفاعلات مع جدول العمال مطابق للموردين"""
        self.employees_table.itemSelectionChanged.connect(self.on_employees_selection_changed)
        self.employees_table.cellDoubleClicked.connect(self.edit_employee)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.employees_table.itemAt(event.pos())
            if item is None:
                self.employees_table.clearSelection()
            QTableWidget.mousePressEvent(self.employees_table, event)

        self.employees_table.mousePressEvent = mousePressEvent

    def on_employees_selection_changed(self):
        """معالج تغيير التحديد في جدول العمال مطابق للموردين"""
        selected_items = self.employees_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد (إذا كانت موجودة)
        if hasattr(self, 'edit_employee_button'):
            self.edit_employee_button.setEnabled(has_selection)
        if hasattr(self, 'delete_employee_button'):
            self.delete_employee_button.setEnabled(has_selection)

    def edit_employee(self):
        """تعديل بيانات عامل - دالة مؤقتة"""
        try:
            employee_id, error = self.get_selected_employee_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"تعديل العامل رقم: {employee_id}")
            # TODO: إضافة نافذة تعديل العامل
        except Exception as e:
            print(f"خطأ في تعديل العامل: {str(e)}")

    def get_selected_employee_id(self):
        """استخراج معرف العامل المحدد من الجدول مطابق للموردين"""
        try:
            selected_row = self.employees_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار عامل من القائمة"

            if not self.employees_table.item(selected_row, 0):
                return None, "الرجاء اختيار عامل صالح من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.employees_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم العامل"

            employee_id = int(numbers[0])
            return employee_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف العامل: {str(e)}"

    def refresh_employees_data(self):
        """تحديث بيانات جدول العمال"""
        try:
            # جلب جميع العمال من قاعدة البيانات (من الأقدم للأحدث)
            employees = self.session.query(Employee).order_by(Employee.id.asc()).all()
            self.populate_employees_table(employees)
            self.update_employees_summary(employees)
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث بيانات العمال: {str(e)}")

    def update_employees_summary(self, employees):
        """تحديث ملخص العمال مطابق للموردين"""
        try:
            # حساب الإحصائيات
            total_employees = len(employees)
            active_employees = len([e for e in employees if (e.balance or 0) > 0])
            debtor_employees = len([e for e in employees if (e.balance or 0) < 0])
            normal_employees = len([e for e in employees if (e.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(e.balance for e in employees if (e.balance or 0) > 0)
            total_negative = sum(abs(e.balance) for e in employees if (e.balance or 0) < 0)
            net_balance = sum(e.balance or 0 for e in employees)

            # تحديث النصوص (إذا كان هناك عنصر ملخص)
            if hasattr(self, 'employees_summary_label'):
                summary_text = (f"إجمالي العمال: {total_employees} | "
                              f"نشط: {active_employees} | "
                              f"مدين: {debtor_employees} | "
                              f"عادي: {normal_employees}")
                self.employees_summary_label.setText(summary_text)

            if hasattr(self, 'employees_balance_label'):
                balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                              f"الأرصدة السالبة: {format_currency(total_negative)} | "
                              f"صافي الرصيد: {format_currency(net_balance)}")
                self.employees_balance_label.setText(balance_text)

        except Exception as e:
            print(f"خطأ في تحديث ملخص العمال: {str(e)}")

    def create_employees_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية للعمال إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود عمال
            employee_count = self.session.query(Employee).count()

            if employee_count == 0:
                # إنشاء عمال تجريبيين مع الحقول الجديدة
                import datetime
                sample_employees = [
                    Employee(
                        name="أحمد محمد علي",
                        address="القاهرة، شبرا الخيمة",
                        phone="01234567890",
                        email="<EMAIL>",
                        position="مهندس مدني",
                        salary=8000.0,
                        balance=2000.0,
                        notes="عامل متميز - أداء ممتاز",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=365)
                    ),
                    Employee(
                        name="فاطمة أحمد حسن",
                        address="الجيزة، الدقي",
                        phone="01098765432",
                        email="<EMAIL>",
                        position="محاسبة",
                        salary=6500.0,
                        balance=-500.0,
                        notes="تحتاج تسوية مالية",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=300)
                    ),
                    Employee(
                        name="محمد عبد الله سالم",
                        address="الإسكندرية، المنتزه",
                        phone="01555123456",
                        email="<EMAIL>",
                        position="فني كهرباء",
                        salary=5500.0,
                        balance=0.0,
                        notes="عامل جديد في فترة التدريب",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=180)
                    ),
                    Employee(
                        name="سارة محمود إبراهيم",
                        address="أسوان، وسط البلد",
                        phone="01777888999",
                        email="<EMAIL>",
                        position="مديرة مشاريع",
                        salary=12000.0,
                        balance=5000.0,
                        notes="قائدة فريق - مسؤولة عن المشاريع الكبرى",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=500)
                    ),
                    Employee(
                        name="خالد عبد الرحمن طه",
                        address="الغردقة، السقالة",
                        phone="01666555444",
                        email="<EMAIL>",
                        position="عامل بناء",
                        salary=4000.0,
                        balance=-1000.0,
                        notes="يحتاج متابعة للحضور والانصراف",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=120)
                    )
                ]

                # إضافة العمال لقاعدة البيانات
                for employee in sample_employees:
                    self.session.add(employee)

                # حفظ التغييرات
                self.session.commit()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            print(f"خطأ في إنشاء البيانات التجريبية للعمال: {str(e)}")

    def populate_employees_table(self, employees):
        """ملء جدول العمال بالبيانات مطابق للموردين"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.employees_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.employees_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للموردين
            for row, employee in enumerate(employees):
                try:
                    self.employees_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة مطابق للموردين
                    id_item = QTableWidgetItem(f"🔢 #{str(employee.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🔢 الرقم التسلسلي: {employee.id}")
                    self.employees_table.setItem(row, 0, id_item)

                    # 2. اسم العامل مع تنسيق احترافي
                    name_text = employee.name if employee.name and employee.name.strip() else "No data"
                    name_item = QTableWidgetItem(f"👷‍♂️ {name_text}")
                    name_item.setTextAlignment(Qt.AlignCenter)
                    name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if name_text == "No data":
                        name_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        name_item.setForeground(QColor("#1e40af"))
                    name_item.setToolTip(f"👷‍♂️ اسم العامل: {name_text}\n💡 انقر نقرتين للتعديل")
                    self.employees_table.setItem(row, 1, name_item)

                    # 3. العنوان مع تنسيق جذاب
                    address_text = employee.address if employee.address and employee.address.strip() else "No data"
                    address_item = QTableWidgetItem(f"🏠 {address_text}")
                    address_item.setTextAlignment(Qt.AlignCenter)
                    address_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if address_text == "No data":
                        address_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        address_item.setForeground(QColor("#065f46"))
                    address_item.setToolTip(f"🏠 العنوان: {address_text}")
                    self.employees_table.setItem(row, 2, address_item)

                    # 4. البريد الإلكتروني مع تنسيق أنيق
                    email_text = employee.email if employee.email and employee.email.strip() else "No data"
                    email_item = QTableWidgetItem(f"📧 {email_text}")
                    email_item.setTextAlignment(Qt.AlignCenter)
                    email_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if email_text == "No data":
                        email_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        email_item.setForeground(QColor("#374151"))
                    email_item.setToolTip(f"📧 البريد الإلكتروني: {email_text}")
                    self.employees_table.setItem(row, 3, email_item)

                    # 5. رقم الهاتف مع تنسيق متطور
                    phone_text = employee.phone if employee.phone and employee.phone.strip() else "No data"
                    phone_item = QTableWidgetItem(f"📱 {phone_text}")
                    phone_item.setTextAlignment(Qt.AlignCenter)
                    phone_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if phone_text == "No data":
                        phone_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        phone_item.setForeground(QColor("#374151"))
                    phone_item.setToolTip(f"📱 رقم الهاتف: {phone_text}")
                    self.employees_table.setItem(row, 4, phone_item)

                    # 6. الرصيد مع تنسيق مالي متطور مطابق للموردين
                    try:
                        balance = employee.balance or 0
                        if balance and balance != 0:
                            balance_formatted = f"{int(abs(balance)):,}".replace(',', '٬')
                            if balance > 0:
                                balance_display = f"💎 {balance_formatted} جنيه"
                                balance_color = QColor("#059669")  # أخضر للموجب
                            else:
                                balance_display = f"💸 {balance_formatted} جنيه"
                                balance_color = QColor("#dc2626")  # أحمر للسالب
                        else:
                            balance_display = "💵 0 جنيه"
                            balance_color = QColor("#6b7280")  # رمادي للصفر
                    except Exception:
                        balance_display = "💵 0 جنيه"
                        balance_color = QColor("#6b7280")

                    balance_item = QTableWidgetItem(balance_display)
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    balance_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    balance_item.setForeground(balance_color)
                    balance_item.setToolTip(f"💵 رصيد العامل: {balance_display}")
                    self.employees_table.setItem(row, 5, balance_item)

                    # 7. حالة العامل مع تصميم متطور ومؤشرات بصرية مطابق للموردين
                    status_map = {
                        'active': {
                            'text': '🎉 نشط',
                            'color': QColor("#059669"),
                            'tooltip': '🎉 العامل نشط ولديه رصيد موجب\n✅ يمكن التعامل معه'
                        },
                        'normal': {
                            'text': '🎯 عادي',
                            'color': QColor("#d97706"),
                            'tooltip': '🎯 العامل برصيد صفر\n💡 حالة عادية'
                        },
                        'debtor': {
                            'text': '⚠️ مدين',
                            'color': QColor("#dc2626"),
                            'tooltip': '⚠️ العامل مدين برصيد سالب\n🔄 يتطلب متابعة'
                        }
                    }

                    # تحديد حالة العامل
                    if balance > 0:
                        status_key = 'active'
                    elif balance == 0:
                        status_key = 'normal'
                    else:
                        status_key = 'debtor'

                    status_info = status_map.get(status_key, {
                        'text': f"❓ غير محدد",
                        'color': QColor("#6b7280"),
                        'tooltip': f"❓ حالة غير معروفة"
                    })

                    status_item = QTableWidgetItem(status_info['text'])
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    status_item.setForeground(status_info['color'])
                    status_item.setToolTip(status_info['tooltip'])
                    self.employees_table.setItem(row, 6, status_item)

                    # 8. الملاحظات مع تنسيق أنيق
                    notes_text = employee.notes if employee.notes and employee.notes.strip() else "No data"
                    notes_item = QTableWidgetItem(f"📋 {notes_text}")
                    notes_item.setTextAlignment(Qt.AlignCenter)
                    notes_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if notes_text == "No data":
                        notes_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        notes_item.setForeground(QColor("#374151"))
                    notes_item.setToolTip(f"📋 الملاحظات: {notes_text}")
                    self.employees_table.setItem(row, 7, notes_item)

                    # 9. تاريخ التوظيف مع تنسيق التاريخ
                    try:
                        if hasattr(employee, 'hire_date') and employee.hire_date:
                            date_formatted = employee.hire_date.strftime("%d/%m/%Y")
                            date_display = f"🗓️ {date_formatted}"
                        else:
                            date_display = "🗓️ غير محدد"
                    except Exception:
                        date_display = "🗓️ غير محدد"

                    date_item = QTableWidgetItem(date_display)
                    date_item.setTextAlignment(Qt.AlignCenter)
                    date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    date_item.setForeground(QColor("#374151"))
                    date_item.setToolTip(f"🗓️ تاريخ توظيف العامل: {date_display}")
                    self.employees_table.setItem(row, 8, date_item)

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.employees_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.employees_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول العمال: {str(e)}")
